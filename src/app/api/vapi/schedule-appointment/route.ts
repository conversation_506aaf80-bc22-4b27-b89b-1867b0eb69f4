import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Parse the request body from Vapi
    const body = await request.json();
    
    // Log the incoming request for debugging
    console.log('Vapi appointment scheduling request:', body);
    
    // Extract tool call information
    const { toolCallList } = body;
    
    if (!toolCallList || !Array.isArray(toolCallList)) {
      return NextResponse.json({
        error: 'Invalid request format'
      }, { status: 400 });
    }
    
    // Process each tool call
    const results = toolCallList.map((toolCall: any) => {
      console.log('Processing tool call:', toolCall);
      
      return {
        toolCallId: toolCall.id,
        result: "Appointment created successfully! I've added it to your focus section."
      };
    });
    
    // Return response in Vapi's expected format
    return NextResponse.json({
      results
    });
    
  } catch (error) {
    console.error('Error processing appointment request:', error);
    
    return NextResponse.json({
      error: 'Failed to process appointment request'
    }, { status: 500 });
  }
}
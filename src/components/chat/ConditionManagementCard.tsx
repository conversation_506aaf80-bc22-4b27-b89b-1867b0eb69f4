import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  HeartHandshake, 
  Video, 
  Pill, 
  Users 
} from "lucide-react";

interface ConditionManagementData {
  condition: string;
  features: {
    nurseLineTitle: string;
    nurseLineDescription: string;
    telehealthTitle: string;
    telehealthDescription: string;
    medicationTitle: string;
    medicationDescription: string;
    careTeamTitle: string;
    careTeamDescription: string;
  };
}

const defaultConditionData: ConditionManagementData = {
  condition: "Crohn's",
  features: {
    nurseLineTitle: "A dedicated Nurse Coordinator Line",
    nurseLineDescription: "Your go-to nurse for support, guidance, and answers. They’ll help you navigate care, appointments, and lifestyle changes.",
    telehealthTitle: "Specialist telehealth access",
    telehealthDescription: "Virtual visits with GI specialists, plus help with scheduling and getting the most from each appointment",
    medicationTitle: "Easy medication management",
    medicationDescription: "Set up refills, get delivery reminders, and avoid drug interactions—all in one place.",
    careTeamTitle: "Connected care support",
    careTeamDescription: "Your symptoms, meds, and care plans stay aligned across your healthcare team."
  }
};

interface ConditionManagementCardProps {
  data?: ConditionManagementData;
  isLoading?: boolean;
  onScheduleCallBack?: () => void;
  onScheduleTelehealth?: () => void;
  onMedicationHelp?: () => void;
}

const ConditionManagementCard: React.FC<ConditionManagementCardProps> = ({
  data = defaultConditionData,
  isLoading = false,
  onScheduleCallBack,
  onScheduleTelehealth,
  onMedicationHelp
}) => {
  if (isLoading) {
    return (
      <Card className="w-full max-w-none sm:max-w-2xl">
        <CardHeader>
          <div className="h-6 w-64 bg-gray-200 rounded animate-pulse" />
        </CardHeader>
        <CardContent className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
              <div className="h-3 w-full bg-gray-100 rounded animate-pulse" />
              <div className="h-3 w-3/4 bg-gray-100 rounded animate-pulse" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  const safeData = {
    condition: data?.condition ?? defaultConditionData.condition,
    features: {
      nurseLineTitle: data?.features?.nurseLineTitle ?? defaultConditionData.features.nurseLineTitle,
      nurseLineDescription: data?.features?.nurseLineDescription ?? defaultConditionData.features.nurseLineDescription,
      telehealthTitle: data?.features?.telehealthTitle ?? defaultConditionData.features.telehealthTitle,
      telehealthDescription: data?.features?.telehealthDescription ?? defaultConditionData.features.telehealthDescription,
      medicationTitle: data?.features?.medicationTitle ?? defaultConditionData.features.medicationTitle,
      medicationDescription: data?.features?.medicationDescription ?? defaultConditionData.features.medicationDescription,
      careTeamTitle: data?.features?.careTeamTitle ?? defaultConditionData.features.careTeamTitle,
      careTeamDescription: data?.features?.careTeamDescription ?? defaultConditionData.features.careTeamDescription,
    }
  };

  const features = [
    {
      icon: HeartHandshake,
      title: safeData.features.nurseLineTitle,
      description: safeData.features.nurseLineDescription,
      color: "text-blue-600",
      buttonConfig: {
        label: "Schedule Call Back",
        onClick: onScheduleCallBack,
        className: "border-blue-300 dark:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-500/10 text-blue-700 dark:text-blue-300"
      }
    },
    {
      icon: Video,
      title: safeData.features.telehealthTitle,
      description: safeData.features.telehealthDescription,
      color: "text-purple-600",
      buttonConfig: {
        label: "Schedule Telehealth",
        onClick: onScheduleTelehealth,
        className: "border-purple-300 dark:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-500/10 text-purple-700 dark:text-purple-300"
      }
    },
    {
      icon: Pill,
      title: safeData.features.medicationTitle,
      description: safeData.features.medicationDescription,
      color: "text-orange-600",
      buttonConfig: {
        label: "Get Medication Help",
        onClick: onMedicationHelp,
        className: "border-orange-300 dark:border-orange-500 hover:bg-orange-50 dark:hover:bg-orange-500/10 text-orange-700 dark:text-orange-300"
      }
    },
    {
      icon: Users,
      title: safeData.features.careTeamTitle,
      description: safeData.features.careTeamDescription,
      color: "text-teal-600"
    }
  ];

  return (
    <Card className="w-full max-w-none sm:max-w-2xl">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <CardTitle className="text-xl font-semibold">
            Personalized Condition Managements
          </CardTitle>
          <Badge variant="secondary" className="bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200">
            {safeData.condition}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6 pt-0">
        {features.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <div key={index}>
              <div className="flex items-start gap-4">
                <div className={`flex-shrink-0 p-2 rounded-lg bg-gray-50 dark:bg-gray-800 ${feature.color}`}>
                  <IconComponent size={20} />
                </div>
                <div className="flex-1 space-y-2">
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                  {feature.buttonConfig && feature.buttonConfig.onClick && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={feature.buttonConfig.onClick}
                      className={`mt-3 text-xs ${feature.buttonConfig.className}`}
                    >
                      {feature.buttonConfig.label}
                    </Button>
                  )}
                </div>
              </div>
              {index < features.length - 1 && (
                <Separator className="mt-6" />
              )}
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

export default ConditionManagementCard;
